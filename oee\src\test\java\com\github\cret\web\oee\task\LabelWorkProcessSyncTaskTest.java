package com.github.cret.web.oee.task;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.github.cret.web.oee.service.LabelWorkProcessAnalysisService;

/**
 * 标签工艺过程分析与QC不良项目组合数据同步定时任务测试
 */
@ExtendWith(MockitoExtension.class)
class LabelWorkProcessSyncTaskTest {

	@Mock
	private LabelWorkProcessAnalysisService labelWorkProcessAnalysisService;

	@InjectMocks
	private LabelWorkProcessSyncTask labelWorkProcessSyncTask;

	@Test
	void testSyncLabelWorkProcessAnalysisWithQcBadItems() {
		// 模拟服务返回
		when(labelWorkProcessAnalysisService.syncAllLinesCurrentDayDataWithQcBadItems()).thenReturn(10);

		// 执行定时任务
		labelWorkProcessSyncTask.syncLabelWorkProcessAnalysisWithQcBadItems();

		// 验证服务方法被调用
		verify(labelWorkProcessAnalysisService, times(1)).syncAllLinesCurrentDayDataWithQcBadItems();
	}

	@Test
	void testSyncLabelWorkProcessAnalysisWithQcBadItemsException() {
		// 模拟服务抛出异常
		when(labelWorkProcessAnalysisService.syncAllLinesCurrentDayDataWithQcBadItems())
			.thenThrow(new RuntimeException("测试异常"));

		// 执行定时任务（不应该抛出异常）
		labelWorkProcessSyncTask.syncLabelWorkProcessAnalysisWithQcBadItems();

		// 验证服务方法被调用
		verify(labelWorkProcessAnalysisService, times(1)).syncAllLinesCurrentDayDataWithQcBadItems();
	}

}
