package com.github.cret.web.oee.document;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import com.github.cret.web.oee.enums.LineType;

/**
 * 线体
 */
@Document("t_production_line")
public class ProductionLine {

	@Id
	private String id;

	// 线体名称
	@Field(name = "name")
	String name;

	// 线体编号
	@Field(name = "code")
	String code;

	// 车间编码
	@Field(name = "workshop_code")
	String workshopCode;

	// 线体类型 (1、单轨，2、双轨)
	@Field(name = "type")
	LineType type;

	// 0为禁用, 1为启用
	@Field(name = "enable")
	private Integer enable;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getWorkshopCode() {
		return workshopCode;
	}

	public void setWorkshopCode(String workshopCode) {
		this.workshopCode = workshopCode;
	}

	public LineType getType() {
		return type;
	}

	public void setType(LineType type) {
		this.type = type;
	}

	public Integer getEnable() {
		return enable;
	}

	public void setEnable(Integer enable) {
		this.enable = enable;
	}

}
