package com.github.cret.web.oee.service.impl;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.github.cret.web.oee.client.MesService;
import com.github.cret.web.oee.document.ProductionLine;
import com.github.cret.web.oee.document.mes.LabelWorkProcessWithQcBadItemsDoc;
import com.github.cret.web.oee.domain.mes.LabelWorkProcessWithQcBadItems;
import com.github.cret.web.oee.repository.LabelWorkProcessWithQcBadItemsRepository;
import com.github.cret.web.oee.service.LabelWorkProcessWithQcBadItemsService;
import com.github.cret.web.oee.service.ProductionLineService;

@Service
public class LabelWorkProcessWithQcBadItemsServiceImpl implements LabelWorkProcessWithQcBadItemsService {

	private static final Logger logger = LoggerFactory.getLogger(LabelWorkProcessWithQcBadItemsServiceImpl.class);

	private final LabelWorkProcessWithQcBadItemsRepository repository;

	private final MesService mesService;

	private final ProductionLineService productionLineService;

	public LabelWorkProcessWithQcBadItemsServiceImpl(LabelWorkProcessWithQcBadItemsRepository repository,
			MesService mesService, ProductionLineService productionLineService) {
		this.repository = repository;
		this.mesService = mesService;
		this.productionLineService = productionLineService;
	}

	@Override
	public List<LabelWorkProcessWithQcBadItemsDoc> getLabelWorkProcessWithQcBadItems(String plId, Date startDate,
			Date endDate) {
		return repository.findByPlIdAndFinalWpCmpDateBetween(plId, startDate, endDate);
	}

	@Override
	public int syncLabelWorkProcessWithQcBadItems(String plId, Date startDate, Date endDate) {
		try {
			logger.info("开始同步线体 {} 的标签工艺过程分析与QC不良项目组合数据，时间范围：{} - {}", plId, startDate, endDate);

			// 调用MES接口获取组合数据
			List<LabelWorkProcessWithQcBadItems> mesData = mesService.fetchLabelWorkProcessAnalysisWithQcBadItems(plId,
					startDate, endDate);

			if (mesData == null || mesData.isEmpty()) {
				logger.info("线体 {} 在指定时间范围内没有标签工艺过程分析与QC不良项目组合数据", plId);
				return 0;
			}

			// 批量保存或更新数据
			int savedCount = batchSaveOrUpdate(mesData);
			logger.info("线体 {} 同步完成，共处理 {} 条数据", plId, savedCount);

			return savedCount;
		}
		catch (Exception e) {
			logger.error("同步线体 {} 的标签工艺过程分析与QC不良项目组合数据失败", plId, e);
			return 0;
		}
	}

	@Override
	public int syncAllLinesCurrentDayData() {
		logger.info("开始同步所有线体当天的标签工艺过程分析与QC不良项目组合数据");

		// 获取当天的时间范围
		LocalDate today = LocalDate.now();
		LocalDateTime startOfDay = today.atTime(8, 0, 0); // 早班8点开始
		LocalDateTime endOfDay = today.plusDays(1).atTime(8, 0, 0); // 次日早班8点结束

		Date startDate = Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
		Date endDate = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());

		// 获取所有启用的生产线
		List<ProductionLine> productionLines = productionLineService.getList()
			.stream()
			.filter(line -> line.getEnable() != null && line.getEnable() == 1)
			.toList();

		int totalSyncCount = 0;

		for (ProductionLine line : productionLines) {
			try {
				// 线体间查询间隔，避免短时间大量查询
				if (totalSyncCount > 0) {
					Thread.sleep(3000); // 间隔3秒
				}

				int syncCount = syncLabelWorkProcessWithQcBadItems(line.getMesCode(), startDate, endDate);
				totalSyncCount += syncCount;

			}
			catch (InterruptedException e) {
				logger.warn("线体间查询间隔被中断", e);
				Thread.currentThread().interrupt();
			}
			catch (Exception e) {
				logger.error("同步线体 {} 数据时发生异常", line.getMesCode(), e);
			}
		}

		logger.info("所有线体当天数据同步完成，共同步 {} 条数据", totalSyncCount);
		return totalSyncCount;
	}

	@Override
	public LabelWorkProcessWithQcBadItemsDoc saveOrUpdate(LabelWorkProcessWithQcBadItems data) {
		if (data == null) {
			return null;
		}

		// 转换为文档对象
		LabelWorkProcessWithQcBadItemsDoc doc = convertToDoc(data);

		// 设置同步时间
		doc.setSyncTime(new Date());

		// 检查是否已存在相同的记录
		Optional<LabelWorkProcessWithQcBadItemsDoc> existingData = repository.findByLbIdAndPlId(data.getLbId(),
				data.getPlId());

		if (existingData.isPresent()) {
			// 更新现有记录
			LabelWorkProcessWithQcBadItemsDoc existing = existingData.get();
			existing.setLbGrp(doc.getLbGrp());
			existing.setMo(doc.getMo());
			existing.setKeyWpFirstResult(doc.getKeyWpFirstResult());
			existing.setFinalResult(doc.getFinalResult());
			existing.setFinalWpCmpDate(doc.getFinalWpCmpDate());
			existing.setQcBadItems(doc.getQcBadItems());
			existing.setSyncTime(doc.getSyncTime());
			return repository.save(existing);
		}
		else {
			// 插入新记录
			return repository.save(doc);
		}
	}

	@Override
	public int batchSaveOrUpdate(List<LabelWorkProcessWithQcBadItems> dataList) {
		if (dataList == null || dataList.isEmpty()) {
			return 0;
		}

		int savedCount = 0;
		for (LabelWorkProcessWithQcBadItems data : dataList) {
			try {
				saveOrUpdate(data);
				savedCount++;
			}
			catch (Exception e) {
				logger.error("保存标签工艺过程分析与QC不良项目组合数据失败：{}", data, e);
			}
		}

		return savedCount;
	}

	@Override
	public List<LabelWorkProcessWithQcBadItemsDoc> findByLbIds(List<String> lbIds) {
		return repository.findByLbIdIn(lbIds);
	}

	@Override
	public List<LabelWorkProcessWithQcBadItemsDoc> findFailedLabels(String plId, String keyWpFirstResult) {
		return repository.findByPlIdAndKeyWpFirstResult(plId, keyWpFirstResult);
	}

	/**
	 * 将领域对象转换为文档对象
	 * @param data 领域对象
	 * @return 文档对象
	 */
	private LabelWorkProcessWithQcBadItemsDoc convertToDoc(LabelWorkProcessWithQcBadItems data) {
		LabelWorkProcessWithQcBadItemsDoc doc = new LabelWorkProcessWithQcBadItemsDoc();
		doc.setLbId(data.getLbId());
		doc.setLbGrp(data.getLbGrp());
		doc.setPlId(data.getPlId());
		doc.setMo(data.getMo());
		doc.setKeyWpFirstResult(data.getKeyWpFirstResult());
		doc.setFinalResult(data.getFinalResult());
		doc.setFinalWpCmpDate(data.getFinalWpCmpDate());
		doc.setQcBadItems(data.getQcBadItems());
		return doc;
	}

}
