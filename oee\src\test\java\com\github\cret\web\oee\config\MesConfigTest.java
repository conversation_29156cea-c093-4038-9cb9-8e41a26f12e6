package com.github.cret.web.oee.config;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * MES配置类测试
 */
@SpringBootTest
@ActiveProfiles("test")
class MesConfigTest {

	@Autowired
	private MesConfig mesConfig;

	@Test
	void testMesConfigLoaded() {
		assertNotNull(mesConfig, "MesConfig should be loaded");
	}

	@Test
	void testLabelWorkProcessUrl() {
		String expectedUrl = "http://localhost:9996/oee-mes-server/label-work-process";
		assertEquals(expectedUrl, mesConfig.getLabelWorkProcessUrl(), 
			"Label work process URL should match configuration");
	}

	@Test
	void testSampleUrl() {
		String expectedUrl = "http://localhost:9996/oee-mes-server/sample";
		assertEquals(expectedUrl, mesConfig.getSampleUrl(), 
			"Sample URL should match configuration");
	}

	@Test
	void testLabelWorkProcessAnalysisUrl() {
		String expectedUrl = "http://localhost:9996/oee-mes-server/oee";
		assertEquals(expectedUrl, mesConfig.getLabelWorkProcessAnalysisUrl(), 
			"Label work process analysis URL should match configuration");
	}

}
