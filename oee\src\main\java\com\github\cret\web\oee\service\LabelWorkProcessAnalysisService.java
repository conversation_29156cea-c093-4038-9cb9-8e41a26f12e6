package com.github.cret.web.oee.service;

import java.util.Date;
import java.util.List;

import com.github.cret.web.oee.document.mes.LabelWorkProcessAnalysis;

/**
 * 标签工艺过程分析服务接口
 */
public interface LabelWorkProcessAnalysisService {

	/**
	 * 根据线体ID和时间范围查询标签工艺过程分析数据
	 * @param plId 线体ID
	 * @param startDate 开始时间
	 * @param endDate 结束时间
	 * @return 标签工艺过程分析数据列表
	 */
	List<LabelWorkProcessAnalysis> getLabelWorkProcessAnalysis(String plId, Date startDate, Date endDate);

	/**
	 * 同步指定线体的标签工艺过程分析数据
	 * @param plId 线体ID
	 * @param startDate 开始时间
	 * @param endDate 结束时间
	 * @return 同步的数据条数
	 */
	int syncLabelWorkProcessAnalysis(String plId, Date startDate, Date endDate);

	/**
	 * 同步所有线体当天的标签工艺过程分析数据
	 * @return 同步的总数据条数
	 */
	int syncAllLinesCurrentDayData();

	/**
	 * 保存或更新标签工艺过程分析数据
	 * @param data 标签工艺过程分析数据
	 * @return 保存后的数据
	 */
	LabelWorkProcessAnalysis saveOrUpdate(LabelWorkProcessAnalysis data);

	/**
	 * 批量保存或更新标签工艺过程分析数据
	 * @param dataList 标签工艺过程分析数据列表
	 * @return 保存的数据条数
	 */
	int batchSaveOrUpdate(List<LabelWorkProcessAnalysis> dataList);
}
