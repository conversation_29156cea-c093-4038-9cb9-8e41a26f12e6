---
logging:
  level:
    '[org.springframework.data.mongodb]': debug
security:
  jwt:
    public-key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4AtQUr4gzjygL+YI8LV52HCpq/l3nX1Za4b8CvgzNiPxsBVeQ9t09VEZNc8LsDypyjtQhOQoqklh1GDBOX/63ypoikdvx7t1XF6CqTZwsa1i3FEH5SXrrGIhNws2ny2R9sr47DwGLAVTxbE3g7UEMqzJlD7yUiBUPm7jP5v42NcqpXN8bJlA17iUoSYj3ORFjl+UZjB6KI4sdiYv6y2W+GSSvwDMFDCs6fpclgUmUEn/ZH1qgmZmHT8rMwMPPqOP6LJOx1UpbqQRaO0eqwJcVdfHesoRyJBUQkPXdPxzk+5gY6UZKE9rx4GEupHO8F/uiKclS8BYXyVKV5P51ZcsTwIDAQAB
    private-key: >-
      MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDgC1BSviDOPKAv5gjwtXnYcKmr+XedfVlrhvwK+DM2I/GwFV5D23T1URk1zwuwPKnKO1CE5CiqSWHUYME5f/rfKmiKR2/Hu3VcXoKpNnCxrWLcUQflJeusYiE3CzafLZH2yvjsPAYsBVPFsTeDtQQyrMmUPvJSIFQ+buM/m/jY1yqlc3xsmUDXuJShJiPc5EWOX5RmMHoojix2Ji/rLZb4ZJK/AMwUMKzp+lyWBSZQSf9kfWqCZmYdPyszAw8+o4/osk7HVSlupBFo7R6rAlxV18d6yhHIkFRCQ9d0/HOT7mBjpRkoT2vHgYS6kc7wX+6IpyVLwFhfJUpXk/nVlyxPAgMBAAECggEAOvh/YegNdntapoDj5Ye+1IuC/XMiWt+9g6H6AWEnCZ8YZIKbVz0SYefAvPsMVo0BG+vU28W6NmguSdpVwa6/97GP5qW69FWRJIjz11ou+3VpyhfB/jym9a62k2huLiOugckXEAcUkM2uKSDmPdKNklEMleqjmZ5MzkodooaJT3mEvGOLPTMXkrWUaIYPU3Z+vIaHfITJrbQmY+WiIFpPfQn4niGZtuYURaxVwVL4wCxrg50Z/P1TVqOSzJxoeuir07isvBVyb2O9QHZFejbkeJ09ywyjPHqCOApRuthhYG8nheHvbnyKG4Mmd/hBRMGzW7LM0YiJKdvYpyZFmUqTMQKBgQD+5T5c/+WfAJz1Rsderizw/QBtEII1QUks/krCbkVk8nSigfE77FQANXB+2atjAczqOE/VhIHDDpmYzrrxyfkddE9y6x5j/TR7ad7W3yeRD7OeeIJK7N6u8l3P8RljTaAGj6k2SxPC/cjuLa46h0w9D6N8n701a2KGf5LgMoqw/QKBgQDhA9jCsIxDdQY0rWk/+wOjPMqR0KzmoPUYR+v1sek8kdUkNW3zlIcdvBklRbvjQZbwi6c4pv8yW76wwLh4w94kB/uuK24mCvlToFISuBx34JObXbi9MfsYpRrssiDqcuEYXZ+eHco9IjYrWuUqBKazy/iFURQpxeNvtmvSjpiKOwKBgQDl5xD7wtdyNZRcao2SaVkht6D1vkkjDZKZ4Xa+rcUrisAGf1t9J+iQE6M21s+Nymbv7wCa3hxIOdfyCqx1Py3REkJCrazIlO4MTm5usfI3Fcl1qs4iFEm50+MnlhfHCJHIuTTvQoI9grgDk/Is2jG6dWuJEW8QSp4gH9rzGsMbZQKBgC47FRkDHv5pF8pQ6rP7hcbPIauN3UhRP6oqdL95Ozt3lvwmgh2uoYgpcl0nE7BgdHudpdDM9tfI48f7AKvHSmfKwBoL0Ei+rnVRFivEagzzNkCQo/kzQKzSl1KUUpD442iXK8lXw4qF8e08cqMMa5e7S6dQfj0K7GyyZTrQk2SrAoGAEERhJ/ui5qC1dhW2P1cLQs9nhBezZ842UX7ICK+iomMG78b1zpu4iSGfbcep0u7Ro0VwD0rx70741qL2x4BGZ+VR5xdqTqS/y+DIH//qN6H+pRQCL2wbWr/J5ZFEJ8I9tbd6dp0joGuowETE8+yw16uHNemO0kFzXtyo07v7myQ=
    access-token-expires: 86400
    refresh-token-expires: 604800
  white-list:
    - /workshop/list
    - /hello
    - /auth/login
    - /error
    - /Npm/*
    - /Samsung/*
    - /log/**
    - /line/**
    - /device/**
    - /analyze/**
    - /theoretical-output/**
    - /feedback-trigger-solutions/**
    - /feedback-trigger-records/**
    - /feedback-trigger-send/**
    - /url/decrypt
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    configprops:
      show-values: always
    health:
      show-details: always
---
server:
  port: 8091
spring:
  jackson:
    default-property-inclusion: NON_NULL
  session:
    timeout: 30s
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration
  web:
    resources:
      add-mappings: false
  data:
    mongodb:
      database: oee
      uri: ************************************************/
      ssl:
        enabled: false
casdoor:
  endpoint: http://*************:8000
  client-id: 2c1306cac7692e637120
  client-secret: fc569266fd7f6bbddcc20f871f1a4dd4438d7f2d
  certificate: |
        -----BEGIN CERTIFICATE-----
        MIIE2TCCAsGgAwIBAgIDAeJAMA0GCSqGSIb3DQEBCwUAMCYxDjAMBgNVBAoTBWFk
        bWluMRQwEgYDVQQDDAtjZXJ0XzJ6cnc0ZTAeFw0yNDA0MzAwMTU3MjhaFw00NDA0
        MzAwMTU3MjhaMCYxDjAMBgNVBAoTBWFkbWluMRQwEgYDVQQDDAtjZXJ0XzJ6cnc0
        ZTCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIBAMKkVqKa4s+/tGlcnQ72
        I8LKZny3FGsBmLo76+vyOoY4DoDyY5lG2gB+GOi6I8054EMD4SjzXOaz16L48g2a
        VsTA+w6wR60RVrjNhf+9QARYSotFAzt0ki9SE4GfObM2A9xkHmGg/N+K0RkQjCiZ
        AQ49JIfj0rJLjHObdWOlRBhfz65BO8QEjefqo5Ur3zDvgw1T123buCMp9YLLB7+0
        /d/yZzhyxZGz20Yxom93IlJ0ehA+ytI+U+kGSnVYRfMAw83Qdo/yohpPbq/+a1Qx
        PptyKz3httrM3WU6q/H0QtymCUPOUSBxlBnsBwJ6KTppZLwx5MA9LTHFzf5uIyN6
        kQ65OO5DZY8792xlgQr4rGmdcOyV5qohFlNZ+xeLXBao+eb/bPSBGsGawFWom9DN
        Am9azAzF/K9vXDGo01CDNNZN40P0PxX66uMAbVXMYwzFsUFwmAqIrf+B/gCx1NxO
        nBeIsi3Uy9Ahv82oX62glVeNHhWQRPKT84BKJGFQX55tFRHyrPOPTw6fSv6LXjMY
        WcYZmRUBPjIYlZTKNkKsTNiyOKKQ9rHHlUA7J3FENj9WZdwOnKTgqJBBhsPjDqjX
        9a5W2HBcn4XeD1s25RD2rj6X1EgdMw+/lcYaphTSRZfXSQcLdVG5bcL46/1X+nnf
        GDTFHUuCyu/dVNgwJGvZZKJXAgMBAAGjEDAOMAwGA1UdEwEB/wQCMAAwDQYJKoZI
        hvcNAQELBQADggIBADRu02bEBSSFBdfywluv0liOAR6cZBWqSMyd+emeUQoafdjo
        qlA5bRmX50D4QWj3r/8GhWikUrmabFmkh2PpHT/aCV6pXdRBF+i85SNC4/i6E4i8
        DwJjGw+dkaNqH9Pfmm2itqWgL4/U4CxgglITIJB7RxtILvUkvAvnK3oF9IPUjarL
        xpfgl6oZfik/c1hgPiqUPtzusn8+CfIV8M/+IasPz7MaVHODgbe5dSVdz+ULoCsx
        YqRcc8QRzgk3VumM3uOuoWLVAvE2VcNa7a+akgOx5PLVgvjMEkRwK1I8Q5CAnx/a
        5dgZUoe1VH+cAOCDnIQqbgovicgBh+8ZH3WMZDywemA4U51W0RkAWahkig++Hq8z
        vBKrXjMthZNjj+JdhSomHwF+0MRmTvuMIc0M6GVysMEY8mEB3avo6WfY/aCyWNPW
        xTaFDKNCaDKCEGu8e4+y82h86epldYIo3MLog0NxvV8x3h2zzejAYiWjFibnrhVs
        8M++RNNlrebU8VLyVuLqiB9JvCtj498b6HO0sl919KpR5xEdME0e17cPyYC9G9FB
        lcC6zNa3lHTWV2/QJCnonWuGr996PV1pOmWWCgy7gIwzLIL/gfLI5CbCR8jkQe+r
        qRLW0vDpgxnRJZ7+bep9K5F8Tl7kyMMf8KDspZQYRq0fqeWbBzDicZ8r+wK8
        -----END CERTIFICATE-----
  organization-name: hongjing_internal
  application-name: ehr
proxy:
  server-url: http://************:8092
wx-work:
  sendSeverUrl: http://************:9997/wx-server
  sendMsgApi: /sendTextCardMsg/cost-analysis
  enableIdTrans: 0
  enableDuplicateCheck: 0
  duplicateCheckInterval: 1800
  safe: 0
  agentId: 1000219
feedback:
  handleBaseUrl: http://************:3000/feedback/handle
  dashboardBaseUrl: http://************:3000/dashboard
  defaultDashboardPath: SMT1-5
mes:
  label-work-process-url: http://oee-mes:9996/oee-mes-server/label-work-process
  sample-url: http://oee-mes:9996/oee-mes-server/sample