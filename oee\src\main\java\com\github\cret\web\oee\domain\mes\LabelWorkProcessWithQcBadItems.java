package com.github.cret.web.oee.domain.mes;

import java.util.Date;
import java.util.List;

/**
 * 标签工艺过程分析与QC不良项目组合数据
 */
public class LabelWorkProcessWithQcBadItems {

	/**
	 * 标签ID
	 */
	private String lbId;

	/**
	 * 标签组
	 */
	private String lbGrp;

	/**
	 * 线体ID
	 */
	private String plId;

	/**
	 * 制造订单号
	 */
	private String mo;

	/**
	 * 关键工序首次结果（仅考虑SMT-03和SMT-06工序的最小IS_PASS值）
	 */
	private String keyWpFirstResult;

	/**
	 * 最终结果（按完成时间排序的最后一个IS_PASS值）
	 */
	private String finalResult;

	/**
	 * 最终工序完成时间
	 */
	private Date finalWpCmpDate;

	/**
	 * QC不良项目列表
	 */
	private List<QcBadItem> qcBadItems;

	public String getLbId() {
		return lbId;
	}

	public void setLbId(String lbId) {
		this.lbId = lbId;
	}

	public String getLbGrp() {
		return lbGrp;
	}

	public void setLbGrp(String lbGrp) {
		this.lbGrp = lbGrp;
	}

	public String getPlId() {
		return plId;
	}

	public void setPlId(String plId) {
		this.plId = plId;
	}

	public String getMo() {
		return mo;
	}

	public void setMo(String mo) {
		this.mo = mo;
	}

	public String getKeyWpFirstResult() {
		return keyWpFirstResult;
	}

	public void setKeyWpFirstResult(String keyWpFirstResult) {
		this.keyWpFirstResult = keyWpFirstResult;
	}

	public String getFinalResult() {
		return finalResult;
	}

	public void setFinalResult(String finalResult) {
		this.finalResult = finalResult;
	}

	public Date getFinalWpCmpDate() {
		return finalWpCmpDate;
	}

	public void setFinalWpCmpDate(Date finalWpCmpDate) {
		this.finalWpCmpDate = finalWpCmpDate;
	}

	public List<QcBadItem> getQcBadItems() {
		return qcBadItems;
	}

	public void setQcBadItems(List<QcBadItem> qcBadItems) {
		this.qcBadItems = qcBadItems;
	}

	@Override
	public String toString() {
		return "LabelWorkProcessWithQcBadItems{" + "lbId='" + lbId + '\'' + ", lbGrp='" + lbGrp + '\'' + ", plId='"
				+ plId + '\'' + ", mo='" + mo + '\'' + ", keyWpFirstResult='" + keyWpFirstResult + '\''
				+ ", finalResult='" + finalResult + '\'' + ", finalWpCmpDate=" + finalWpCmpDate + ", qcBadItems="
				+ qcBadItems + '}';
	}

}
