package com.github.cret.web.oee.controller;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.cret.web.oee.document.mes.LabelWorkProcessAnalysis;
import com.github.cret.web.oee.service.LabelWorkProcessAnalysisService;

/**
 * OEE控制器
 */
@RestController
@RequestMapping("/oee")
public class OeeController {

	private final LabelWorkProcessAnalysisService labelWorkProcessAnalysisService;

	public OeeController(LabelWorkProcessAnalysisService labelWorkProcessAnalysisService) {
		this.labelWorkProcessAnalysisService = labelWorkProcessAnalysisService;
	}

	/**
	 * 获取标签工艺过程分析数据
	 * @param plId 线体ID
	 * @param startDate 开始日期时间（格式：yyyy-MM-dd HH:mm:ss）
	 * @param endDate 结束日期时间（格式：yyyy-MM-dd HH:mm:ss）
	 * @return 标签工艺过程分析数据列表
	 */
	@GetMapping("/label-work-process-analysis")
	public ResponseEntity<List<LabelWorkProcessAnalysis>> getLabelWorkProcessAnalysis(
			@RequestParam String plId,
			@RequestParam String startDate,
			@RequestParam String endDate) {

		try {
			// 解析日期参数
			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			Date start = dateFormat.parse(startDate);
			Date end = dateFormat.parse(endDate);

			// 查询数据
			List<LabelWorkProcessAnalysis> result = labelWorkProcessAnalysisService
				.getLabelWorkProcessAnalysis(plId, start, end);

			return ResponseEntity.ok(result);
		}
		catch (ParseException e) {
			return ResponseEntity.badRequest().build();
		}
		catch (Exception e) {
			return ResponseEntity.internalServerError().build();
		}
	}

	/**
	 * 手动触发同步指定线体的标签工艺过程分析数据
	 * @param plId 线体ID
	 * @param startDate 开始日期时间（格式：yyyy-MM-dd HH:mm:ss）
	 * @param endDate 结束日期时间（格式：yyyy-MM-dd HH:mm:ss）
	 * @return 同步结果
	 */
	@GetMapping("/sync-label-work-process-analysis")
	public ResponseEntity<String> syncLabelWorkProcessAnalysis(
			@RequestParam String plId,
			@RequestParam String startDate,
			@RequestParam String endDate) {

		try {
			// 解析日期参数
			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			Date start = dateFormat.parse(startDate);
			Date end = dateFormat.parse(endDate);

			// 同步数据
			int syncCount = labelWorkProcessAnalysisService.syncLabelWorkProcessAnalysis(plId, start, end);

			return ResponseEntity.ok("同步完成，共处理 " + syncCount + " 条数据");
		}
		catch (ParseException e) {
			return ResponseEntity.badRequest().body("日期格式错误，请使用 yyyy-MM-dd HH:mm:ss 格式");
		}
		catch (Exception e) {
			return ResponseEntity.internalServerError().body("同步失败：" + e.getMessage());
		}
	}

	/**
	 * 手动触发同步所有线体当天的标签工艺过程分析数据
	 * @return 同步结果
	 */
	@GetMapping("/sync-all-lines-current-day")
	public ResponseEntity<String> syncAllLinesCurrentDay() {
		try {
			int totalSyncCount = labelWorkProcessAnalysisService.syncAllLinesCurrentDayData();
			return ResponseEntity.ok("所有线体当天数据同步完成，共同步 " + totalSyncCount + " 条数据");
		}
		catch (Exception e) {
			return ResponseEntity.internalServerError().body("同步失败：" + e.getMessage());
		}
	}
}
