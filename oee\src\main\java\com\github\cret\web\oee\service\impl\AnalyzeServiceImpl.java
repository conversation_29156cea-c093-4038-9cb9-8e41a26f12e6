package com.github.cret.web.oee.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;

import com.github.cret.web.common.enumerate.SysErrEnum;
import com.github.cret.web.oee.calculator.AoiDefectTypesWithMes;
import com.github.cret.web.oee.calculator.AoiQualityWithSerialCalculator;
import com.github.cret.web.oee.calculator.DeviceCtCalculator;
import com.github.cret.web.oee.calculator.HourlyOutputCalculator;
import com.github.cret.web.oee.calculator.LineChangeoverWithThresholdCalculator;
import com.github.cret.web.oee.calculator.OeeCalculator;
import com.github.cret.web.oee.calculator.ProductionDataCalculator;
import com.github.cret.web.oee.document.Device;
import com.github.cret.web.oee.document.ProductionLine;
import com.github.cret.web.oee.document.analyze.TargetOee;
import com.github.cret.web.oee.document.analyze.TheoreticalOutput;
import com.github.cret.web.oee.domain.analyze.AlarmInfo;
import com.github.cret.web.oee.domain.analyze.AnalyzeQuery;
import com.github.cret.web.oee.domain.analyze.AnalyzeResult;
import com.github.cret.web.oee.domain.analyze.AoiProductionGroup;
import com.github.cret.web.oee.domain.analyze.DefectTypeInfo;
import com.github.cret.web.oee.domain.analyze.DefectTypeResult;
import com.github.cret.web.oee.domain.analyze.DeviceCtInfo;
import com.github.cret.web.oee.domain.analyze.DeviceDefectType;
import com.github.cret.web.oee.domain.analyze.HourlyOutput;
import com.github.cret.web.oee.domain.analyze.HourlyOutputList;
import com.github.cret.web.oee.domain.analyze.HourlyRunTimeGroup;
import com.github.cret.web.oee.domain.analyze.LineChangeoverInfo;
import com.github.cret.web.oee.domain.analyze.LineChangeoverRecord;
import com.github.cret.web.oee.domain.analyze.OeeResult;
import com.github.cret.web.oee.domain.analyze.ProductionData;
import com.github.cret.web.oee.domain.analyze.ProductionDataWithDevice;
import com.github.cret.web.oee.domain.analyze.ProductionLineInfo;
import com.github.cret.web.oee.domain.analyze.ProductionLineResult;
import com.github.cret.web.oee.enums.DeviceCategory;
import com.github.cret.web.oee.enums.DeviceType;
import com.github.cret.web.oee.enums.ProductionSample;
import com.github.cret.web.oee.service.AnalyzeService;
import com.github.cret.web.oee.service.DeviceService;
import com.github.cret.web.oee.service.FromMesSampleService;
import com.github.cret.web.oee.service.LbwpService;
import com.github.cret.web.oee.service.ProductService;
import com.github.cret.web.oee.service.ProductionLineService;
import com.github.cret.web.oee.service.TargetOeeService;
import com.github.cret.web.oee.service.TheoreticalOutputService;
import com.github.cret.web.oee.utils.BuilderUtil;
import com.github.cret.web.oee.utils.OeeUtil;
import com.github.cret.web.system.document.Dict;
import com.github.cret.web.system.domain.DictItem;
import com.github.cret.web.system.repository.DictRepository;

/**
 * 分析服务实现类
 */
@Service
public class AnalyzeServiceImpl implements AnalyzeService {

	private final ProductionLineService productionLineService;

	private final DeviceService deviceService;

	private final ProductService productService;

	private final FromMesSampleService mesSampleService;

	private final LbwpService lbwpService;

	private final TheoreticalOutputService theoreticalOutputService;

	private final TargetOeeService targetOeeService;

	private final DictRepository dictRepository;

	private final MongoTemplate mongoTemplate;

	public AnalyzeServiceImpl(DeviceService deviceService, ProductionLineService productionLineService,
			FromMesSampleService mesSampleService, LbwpService lbwpService, ProductService productService,
			TheoreticalOutputService theoreticalOutputService, TargetOeeService targetOeeService,
			DictRepository dictRepository, MongoTemplate mongoTemplate) {
		this.deviceService = deviceService;
		this.productionLineService = productionLineService;
		this.mesSampleService = mesSampleService;
		this.lbwpService = lbwpService;
		this.productService = productService;
		this.theoreticalOutputService = theoreticalOutputService;
		this.targetOeeService = targetOeeService;
		this.dictRepository = dictRepository;
		this.mongoTemplate = mongoTemplate;
	}

	@Override
	public AnalyzeResult getLineOee(AnalyzeQuery query) {
		// 1. 参数校验与基础数据准备
		query.validateTimeRange();
		ProductionLine productionLine = productionLineService.findProductionLineBycode(query.getCode());
		query.setLineType(productionLine.getType());
		// 获取产线所有设备，并按类型分组
		List<Device> allLineDevices = deviceService.getAllDevices(query.getCode());
		Map<DeviceCategory, List<Device>> devicesByCategory = allLineDevices.stream()
			.collect(Collectors.groupingBy(Device::getCategory));
		// 获取SMT和AOI设备
		List<Device> smtDevices = Optional.ofNullable(devicesByCategory.get(DeviceCategory.SMT))
			.orElse(Collections.emptyList());
		List<Device> aoiDevices = Optional.ofNullable(devicesByCategory.get(DeviceCategory.AOI))
			.orElse(Collections.emptyList());
		// 获取SMT首序设备，用于计算换线等信息
		Map<String, Device> primarySmtDevices = smtDevices.stream()
			.collect(Collectors.groupingBy(device -> device.getTrackEnum().getCode(),
					Collectors.collectingAndThen(Collectors.toList(),
							devices -> devices.stream().min(Comparator.comparing(Device::getSort)).orElse(null))));

		// 2. 获取各项业务数据
		// 获取每个设备的生产信息
		List<ProductionDataWithDevice> productionDataWithDevices = getProductionDataWithDevices(query, allLineDevices);
		List<ProductionDataWithDevice> productionDataWithDevicesSmt = productionDataWithDevices.stream()
			.filter(node -> node.getDevice().getCategory().equals(DeviceCategory.SMT))
			.collect(Collectors.toList());
		// 获取生产数据
		Map<String, List<ProductionData>> productData = getProductData(productionDataWithDevicesSmt);

		// 依据生产数据，获取产品对应的瓶颈ct
		Map<String, Double> productMaxCtMap = productData.entrySet()
			.stream()
			.collect(Collectors.toMap(Map.Entry::getKey, entry -> {
				Double maxCt = theoreticalOutputService.findMaxCtByProductModelAndLineCode(entry.getKey(),
						query.getCode());
				return maxCt;
			}));

		// 获取换线信息
		LineChangeoverInfo linChangeoverInfo = getLineChangeoverInfo(query, primarySmtDevices);

		// 获取当前生产线信息
		List<ProductionLineInfo> productionLineInfos = getCurrentProduction(productionDataWithDevices,
				primarySmtDevices);
		// 获取直通和复判的不良类型
		List<DeviceDefectType> deviceDefectTypes = getThroughDefectTypes(query, aoiDevices);
		List<DeviceDefectType> retrialDefectTypes = getRetrialDefectTypes(query, aoiDevices);
		DefectTypeResult firstPassDefectTypes = getDefectTypeResult(deviceDefectTypes);
		DefectTypeResult defectTypeResult = getDefectTypeResult(retrialDefectTypes);
		// 获取设备CT信息
		List<DeviceCtInfo> devicesCtList = getDevicesCt(query, productionDataWithDevices, mongoTemplate,
				productionLineInfos);

		double calculatePlanTime = OeeCalculator.calculatePlanTime(query);
		double calculateActualPlanTime = OeeCalculator.calculateActualPlanTime(linChangeoverInfo.getChangeoverTime(),
				calculatePlanTime);
		double calculateRunTime = OeeCalculator.calculateRunTime(productData, productionLine);
		double calculateStopTime = OeeCalculator.calculateStopTime(productData, productionLine);

		Integer calculateActualProduction = OeeCalculator.calculateActualProduction(productData, productionLine);
		Integer calculatePlanProduction = OeeCalculator.calculatePlanProduction(productData, productionLine,
				productMaxCtMap);
		Integer calculateActualProuductionPoints = OeeCalculator.calculateActualProuductionPoints(productData,
				productService);
		Integer calculatePlanProuductionPoints = OeeCalculator.calculatePlanProuductionPoints(productData,
				productService, productionLine);

		// 不良数计算
		Integer firstPassDefectCount = calculateDefectCount(deviceDefectTypes);
		Integer retrialDefectCount = calculateDefectCount(retrialDefectTypes);

		// OEE三大指标计算 (结果为0-1之间的小数)
		double calculateAvailability = OeeCalculator.calculateAvailability(calculateRunTime, calculateStopTime);
		double calculatePerformance = OeeCalculator.calculatePerformance(calculateActualProduction,
				calculatePlanProduction);
		double calculateQuality = calculateQuality(query, retrialDefectCount, productData); // 使用复判数据计算
		double calculateOee = OeeCalculator.calculateOee(calculatePerformance, calculateAvailability, calculateQuality);

		TargetOee target = targetOeeService.getTargetOeeOrDefault();

		// 组装OEE结果
		OeeResult oeeResult = BuilderUtil.builder(OeeResult::new)
			.with(OeeResult::setLineCode, query.getCode())
			.with(OeeResult::setChangeoverNum, String.valueOf(linChangeoverInfo.getChangeoverNum()))
			.with(OeeResult::setChangeoverTime,
					String.format("%.2f", (double) linChangeoverInfo.getChangeoverTime() / 3600))
			.with(OeeResult::setPlanTime, String.format("%.2f", calculatePlanTime / 3600))
			.with(OeeResult::setActualPlanTime, String.format("%.2f", calculateActualPlanTime / 3600))
			.with(OeeResult::setRunTime, String.format("%.2f", calculateRunTime / 3600))
			.with(OeeResult::setStopTime, String.format("%.2f", calculateStopTime / 3600))
			.with(OeeResult::setActualBoard, String.valueOf(calculateActualProduction))
			.with(OeeResult::setPlanBoard, String.valueOf(calculatePlanProduction))
			.with(OeeResult::setActualBoardPoints, String.valueOf(calculateActualProuductionPoints))
			.with(OeeResult::setPlanBoardPoints, String.valueOf(calculatePlanProuductionPoints))
			.with(OeeResult::setFirstPassDefectCount, String.valueOf(firstPassDefectCount))
			.with(OeeResult::setDefectCount, String.valueOf(retrialDefectCount))
			.with(OeeResult::setAvailability, String.format("%.2f", calculateAvailability * 100))
			.with(OeeResult::setPerformance, String.format("%.2f", calculatePerformance * 100))
			.with(OeeResult::setQuality, String.format("%.2f", calculateQuality * 100))
			.with(OeeResult::setOee, String.format("%.2f", calculateOee * 100))
			.with(OeeResult::setAvailabilityTarget, String.format("%.2f", target.getAvailability() * 100))
			.with(OeeResult::setPerformanceTarget, String.format("%.2f", target.getPerformance() * 100))
			.with(OeeResult::setQualityTarget, String.format("%.2f", target.getQuality() * 100))
			.with(OeeResult::setOeeTarget, String.format("%.2f", target.getOee() * 100))
			.build();

		// 组装产线信息结果
		ProductionLineResult productionLineResult = BuilderUtil.builder(ProductionLineResult::new)
			.with(ProductionLineResult::setProductionLineInfos, productionLineInfos)
			.with(ProductionLineResult::setProductionLineType, productionLine.getType())
			.build();

		// 组装最终返回结果
		return BuilderUtil.builder(AnalyzeResult::new)
			.with(AnalyzeResult::setOeeResult, oeeResult)
			.with(AnalyzeResult::setProductionLineResult, productionLineResult)
			.with(AnalyzeResult::setDeviceCtInfos, devicesCtList)
			.with(AnalyzeResult::setFirstPassDefectTypes, firstPassDefectTypes)
			.with(AnalyzeResult::setRetrialDefectTypes, defectTypeResult)
			.build();
	}

	/**
	 * 获取设备生产数据
	 * @param query
	 * @param devices
	 * @return
	 */
	private List<ProductionDataWithDevice> getProductionDataWithDevices(AnalyzeQuery query, List<Device> devices) {
		List<ProductionDataWithDevice> result = new ArrayList<>();
		for (Device device : devices) {
			List<ProductionData> productionData = ProductionDataCalculator.getDeviceProductionData(device, query,
					mongoTemplate);
			ProductionDataWithDevice productionDataWithDevice = new ProductionDataWithDevice(device, productionData);
			result.add(productionDataWithDevice);
		}
		return result;
	}

	/**
	 * 获取换线信息
	 * @param query
	 * @param primarySmtDevices
	 * @return
	 */
	private LineChangeoverInfo getLineChangeoverInfo(AnalyzeQuery query, Map<String, Device> primarySmtDevices) {
		// 获取换线记录
		List<LineChangeoverRecord> lineChangeoverInfo = LineChangeoverWithThresholdCalculator
			.getLineChangeoverInfo(primarySmtDevices, query, mongoTemplate);
		// 计算换线信息
		LineChangeoverInfo calculateLineChangeInfo = LineChangeoverWithThresholdCalculator
			.calculateLineChangeInfo(lineChangeoverInfo);
		return calculateLineChangeInfo;
	}

	/**
	 * 计算良品率
	 * @param query
	 * @param defectCounts
	 * @param productData
	 * @return
	 */
	private Double calculateQuality(AnalyzeQuery query, Integer defectCounts,
			Map<String, List<ProductionData>> productData) {
		// 获取每个产品的拼板数，对生产数据处理转成map对象，key为productmodel
		Map<String, Integer> flatNumMap = productData.values()
			.stream()
			.flatMap(List::stream)
			.filter(productionData -> productionData.getFlatNum() != null && productionData.getFlatNum() > 0)
			.collect(Collectors.toMap(ProductionData::getOriginalProductModel, ProductionData::getFlatNum,
					(existing, replacement) -> existing));

		// 获取AOI设备
		List<Device> aoiDevices = deviceService.getDevicesByCategory(query.getCode(), DeviceCategory.AOI);

		// AOI生产总数
		int totalCount = aoiDevices.stream()
			// .map(device -> AoiQualityCalculator.getAoiTotalCount(device, query,
			// mongoTemplate))
			.map(device -> AoiQualityWithSerialCalculator.getAoiTotalCount(device, query, mongoTemplate))
			.flatMap(List::stream)
			.mapToInt(group -> calculateGroupTotalCount(group, flatNumMap))
			.sum();

		return totalCount > 0 ? (double) (totalCount - defectCounts) / totalCount : 0.00;
	}

	/**
	 * 计算AOI产品的拼板总数
	 * @param group
	 * @param flatNumMap
	 * @return
	 */
	private int calculateGroupTotalCount(AoiProductionGroup group, Map<String, Integer> flatNumMap) {
		int actualProduction = group.getActualProduction() != null ? group.getActualProduction() : 0;
		// 不需要计算拼板数
		if (!group.getNeedFlat()) {
			return actualProduction;
		}

		// 需要计算拼板数
		Integer flatNum = group.getFlatNum();
		if (flatNum == null) {
			flatNum = OeeUtil.generateCandidateKeys(group.getProductModel())
				.stream()
				.filter(flatNumMap::containsKey)
				.map(flatNumMap::get)
				.findFirst()
				.orElse(1);
		}

		return actualProduction * flatNum;
	}

	/**
	 * 获取首序设备的生产信息
	 * @param productionDataMaps
	 * @param primarySmtDevices
	 * @return
	 */
	private List<ProductionLineInfo> getCurrentProduction(List<ProductionDataWithDevice> productionDataWithDevices,
			Map<String, Device> primarySmtDevices) {
		List<ProductionLineInfo> result = new ArrayList<>();
		Map<String, List<ProductionData>> productionDataMaps = productionDataWithDevices.stream()
			.collect(Collectors.toMap(p -> p.getDevice().getCode(), ProductionDataWithDevice::getProductionData,
					(existing, replacement) -> existing));
		for (Device device : primarySmtDevices.values()) {
			List<ProductionData> productionDataList = productionDataMaps.getOrDefault(device.getCode(),
					Collections.emptyList());

			ProductionData currentProduction = productionDataList.stream()
				.max(Comparator.comparing(ProductionData::getGroupId))
				.orElse(new ProductionData());

			TheoreticalOutput theoreticalOutput = theoreticalOutputService
				.findByDeviceCodeAndProductModel(device.getCode(), currentProduction.getOriginalProductModel());

			ProductionLineInfo lineInfo = BuilderUtil.builder(ProductionLineInfo::new)
				.with(ProductionLineInfo::setOriginalProductModel, currentProduction.getOriginalProductModel())
				.with(ProductionLineInfo::setProductModel, currentProduction.getProductModel())
				.with(ProductionLineInfo::setTrack, device.getTrack())
				.with(ProductionLineInfo::setTrackName, device.getTrackEnum().getName())
				.with(ProductionLineInfo::setSample,
						theoreticalOutput != null ? ProductionSample.notsample : ProductionSample.sample)
				.build();

			result.add(lineInfo);
		}

		return result;
	}

	/**
	 * 获取生产数据汇总
	 * @param productionDataWithDevices
	 * @return
	 */
	private Map<String, List<ProductionData>> getProductData(List<ProductionDataWithDevice> productionDataWithDevices) {

		for (ProductionDataWithDevice productionDataWithDevice : productionDataWithDevices) {
			// 获取分组数据
			List<ProductionData> productionData = productionDataWithDevice.getProductionData();
			List<ProductionData> deviceProductionDataGroup = ProductionDataCalculator
				.getDeviceProductionDataGroup(productionData);
			productionDataWithDevice.setProductionDataGroup(deviceProductionDataGroup);
		}

		Map<String, List<ProductionData>> productModelDataMap = productionDataWithDevices.stream()
			.map(data -> data.getProductionDataGroup())
			.flatMap(List::stream)
			.map(data -> {
				// 查询理论产量
				TheoreticalOutput theoreticalOutput = theoreticalOutputService
					.findByDeviceCodeAndProductModel(data.getDeviceCode(), data.getOriginalProductModel());
				Optional.ofNullable(theoreticalOutput).ifPresent(output -> {
					// 设置理论CT
					Optional.ofNullable(output.getCt()).ifPresent(data::setTheoreticalCt);
					// 设置系统维护的拼板数
					Optional.ofNullable(output.getFlatNumber()).ifPresent(data::setFlatNum);
				});
				return data;
			})
			.collect(Collectors.groupingBy(data -> {
				if (data.getGroupId() == null) {
					return data.getProductModel();
				}
				else {
					return data.getProductModel() + "_" + data.getGroupId();
				}
			}, Collectors.toCollection(ArrayList::new)));

		// 标记瓶颈设备
		productModelDataMap.values().forEach(productDataList -> {
			// 首先尝试根据理论CT找出瓶颈设备
			Optional<ProductionData> theoreticalBottleneck = productDataList.stream()
				.filter(data -> data.getTheoreticalCt() != null)
				.max(Comparator.comparing(ProductionData::getTheoreticalCt));

			// 如果没有找到理论CT的瓶颈设备，则使用实际CT
			if (theoreticalBottleneck.isPresent()) {
				theoreticalBottleneck.get().setBottleneck(true);
			}
			else {
				productDataList.stream()
					.max(Comparator.comparing(ProductionData::getActualCt))
					.ifPresent(data -> data.setBottleneck(true));
			}
		});

		return productModelDataMap;
	}

	/**
	 * 获取设备ct
	 */
	private List<DeviceCtInfo> getDevicesCt(AnalyzeQuery query,
			List<ProductionDataWithDevice> productionDataWithDevices, MongoTemplate mongoTemplate,
			List<ProductionLineInfo> productionLineInfos) {
		Map<String, ProductionLineInfo> lineInfoByTrack = productionLineInfos.stream()
			.filter(info -> info.getTrack() != null)
			.collect(Collectors.toMap(ProductionLineInfo::getTrack, info -> info, (k1, k2) -> k2));

		List<DeviceCtInfo> result = new ArrayList<>();
		for (ProductionDataWithDevice productionDataWithDevice : productionDataWithDevices) {
			Device device = productionDataWithDevice.getDevice();
			ProductionLineInfo productionLineInfo = lineInfoByTrack.get(device.getTrack());

			List<ProductionData> productionDatas = productionDataWithDevice.getProductionData();

			DeviceCtInfo deviceCtInfo = BuilderUtil.builder(DeviceCtInfo::new)
				.with(DeviceCtInfo::setCode, device.getCode())
				.with(DeviceCtInfo::setType, device.getName())
				.with(DeviceCtInfo::setTrackName, device.getTrackEnum().getName())
				.with(DeviceCtInfo::setActualCt,
						DeviceCtCalculator.getDeviceCt(device, query, productionDatas, mongoTemplate))
				.build();

			String productModel = productionDataWithDevice.getProductionData()
				.stream()
				.max(Comparator.comparing(ProductionData::getGroupId))
				.map(ProductionData::getOriginalProductModel)
				.orElse(productionLineInfo.getOriginalProductModel());

			Double theoreticalCt = null;
			if (productModel != null) {
				TheoreticalOutput theoreticalOutput = theoreticalOutputService
					.findByDeviceCodeAndProductModelContaining(device.getCode(), productModel);
				if (theoreticalOutput != null) {
					theoreticalCt = theoreticalOutput.getCt();
				}
			}

			if (theoreticalCt != null) {
				deviceCtInfo.setTheoreticalCt(theoreticalCt);
			}

			result.add(deviceCtInfo);
		}

		// 按轨道分组
		Map<String, List<DeviceCtInfo>> devicesByTrack = result.stream()
			.collect(Collectors.groupingBy(DeviceCtInfo::getTrackName));

		// 遍历每个轨道的设备组
		for (List<DeviceCtInfo> trackDevices : devicesByTrack.values()) {
			// 找出该轨道最大的实际CT值
			double maxActualCt = trackDevices.stream().mapToDouble(DeviceCtInfo::getTheoreticalCt).max().orElse(0.0);

			// 只有当最大CT值大于0时才标记瓶颈工序
			if (maxActualCt > 0) {
				// 找到该轨道第一个具有最大CT值的设备，将其标记为瓶颈工序
				boolean bottleneckSet = false;
				for (DeviceCtInfo deviceCtInfo : trackDevices) {
					if (!bottleneckSet && Math.abs(deviceCtInfo.getTheoreticalCt() - maxActualCt) < 0.001) {
						deviceCtInfo.setBottleneck(true);
						bottleneckSet = true;
					}
					else {
						deviceCtInfo.setBottleneck(false);
					}
				}
			}
			else {
				// 如果该轨道所有设备CT都为0，则都不是瓶颈工序
				trackDevices.forEach(deviceCtInfo -> deviceCtInfo.setBottleneck(false));
			}
		}

		return result;
	}

	@Override
	public HourlyOutputList getHourlyOutput(AnalyzeQuery query) {
		// 参数校验与基础数据准备
		query.validateTimeRange();
		ProductionLine productionLine = productionLineService.findProductionLineBycode(query.getCode());
		query.setLineType(productionLine.getType());

		// 获取SMT设备并验证
		List<Device> devicesByCategory = deviceService.getDevicesByCategory(query.getCode(), DeviceCategory.SMT);
		if (devicesByCategory.isEmpty()) {
			return new HourlyOutputList();
		}

		// 获取实际CT最大的贴片机
		Device maxCtDevice = getSmtByMaxCt(query);
		if (maxCtDevice == null) {
			return new HourlyOutputList();
		}

		// 构建结果对象
		HourlyOutputList result = new HourlyOutputList();
		result.setActualOutputs(getHourlyActualOutput(maxCtDevice, query));

		// 获取并设置理论产出
		List<HourlyOutput> theoreticalOutputs = getHourlyTheoreticalOutput(maxCtDevice, query);
		result.setTheoreticalOutputs(theoreticalOutputs);

		// 设置当前产品理论产出
		result.setCurrentProductTheoreticalOutput(
				!theoreticalOutputs.isEmpty() ? theoreticalOutputs.get(theoreticalOutputs.size() - 1).getCount() : 0);

		return result;
	}

	/**
	 * 获取每小时实际产出
	 * @param device 设备信息
	 * @param query 查询条件
	 * @return 每小时实际产出列表
	 */
	private List<HourlyOutput> getHourlyActualOutput(Device device, AnalyzeQuery query) {
		return switch (device.getType()) {
			case smt_npm_reporter -> HourlyOutputCalculator.getNpmSmtHourlyOutput(device, query, mongoTemplate);
			case smt_npm_reporter2 -> HourlyOutputCalculator.getNpmSmtHourlyOutput(device, query, mongoTemplate);
			case smt_samsung -> HourlyOutputCalculator.getSamsungSmtHourlyOutput(device, query, mongoTemplate);
			case smt_yamaha -> HourlyOutputCalculator.getYamahaSmtHourlyOutput(device, query, mongoTemplate);
			default -> throw new UnsupportedOperationException("该设备类型未实现每小时实际产出查询");
		};
	}

	/**
	 * 获取每小时理论产出
	 * @param device 设备信息
	 * @param query 查询条件
	 * @return 每小时理论产出列表
	 */
	private List<HourlyOutput> getHourlyTheoreticalOutput(Device device, AnalyzeQuery query) {
		// 获取每小时运行时间组并按小时分组
		List<HourlyRunTimeGroup> hourlyRunTimeGroups;
		if (Objects.requireNonNull(device.getType()) == DeviceType.smt_samsung) {
			hourlyRunTimeGroups = HourlyOutputCalculator.getSamsungSmtHourlyRunTime(device, query, mongoTemplate);
		}
		else {
			hourlyRunTimeGroups = HourlyOutputCalculator.getNpmSmtHourlyRunTime(device, query, mongoTemplate);
		}
		Map<String, List<HourlyRunTimeGroup>> hourlyGroups = hourlyRunTimeGroups.stream()
			.collect(Collectors.groupingBy(HourlyRunTimeGroup::getHour));

		List<HourlyOutput> theoreticalOutputs = new ArrayList<>();

		for (Map.Entry<String, List<HourlyRunTimeGroup>> entry : hourlyGroups.entrySet()) {
			String hour = entry.getKey();
			List<HourlyRunTimeGroup> groups = entry.getValue();

			// 按首次生产时间排序
			groups.sort(Comparator.comparing(HourlyRunTimeGroup::getFirstTime));

			if (groups.size() == 1) {
				// 单产品情况处理
				processSingleProductHour(device, hour, groups.get(0), theoreticalOutputs);
			}
			else {
				// 多产品情况处理
				processMultipleProductsHour(device, hour, groups, theoreticalOutputs);
			}
		}

		// 按小时排序
		theoreticalOutputs.sort(Comparator.comparing(HourlyOutput::getTime));
		return theoreticalOutputs;
	}

	/**
	 * 处理单产品小时数据
	 * @param device 设备信息
	 * @param hour 小时
	 * @param group 运行时间组
	 * @param outputs 输出列表
	 */
	private void processSingleProductHour(Device device, String hour, HourlyRunTimeGroup group,
			List<HourlyOutput> outputs) {
		// 获取线体编码
		String lineId = device.getLineId();
		Double lineMaxCt = theoreticalOutputService.findMaxCtByProductModelAndLineCode(group.getProductModel(), lineId);

		// 获取该产品的理论产出配置
		TheoreticalOutput theoreticalOutput = theoreticalOutputService.findByDeviceCodeAndProductModel(device.getCode(),
				group.getProductModel());
		if (theoreticalOutput != null && lineMaxCt != null && lineMaxCt > 0) {
			// 计算每小时理论产出：3600秒/节拍时间*拼板数
			double hourlyOutput = Math.floor(3600 / lineMaxCt * theoreticalOutput.getFlatNumber());

			HourlyOutput output = new HourlyOutput();
			output.setTime(hour);
			output.setCount((int) hourlyOutput);
			outputs.add(output);
		}
	}

	/**
	 * 处理多产品小时数据
	 * @param device 设备信息
	 * @param hour 小时
	 * @param groups 运行时间组列表
	 * @param outputs 输出列表
	 */
	private void processMultipleProductsHour(Device device, String hour, List<HourlyRunTimeGroup> groups,
			List<HourlyOutput> outputs) {
		double totalOutput = 0.0; // 总产出

		// 获取线体编码
		String lineId = device.getLineId();

		for (HourlyRunTimeGroup group : groups) {
			// 获取该产品的理论产出配置
			TheoreticalOutput theoreticalOutput = theoreticalOutputService
				.findByDeviceCodeAndProductModel(device.getCode(), group.getProductModel());
			Double lineMaxCt = theoreticalOutputService.findMaxCtByProductModelAndLineCode(group.getProductModel(),
					lineId);
			if (theoreticalOutput != null && lineMaxCt != null && lineMaxCt > 0) {
				// 计算每小时理论产出：3600秒/节拍时间*拼板数
				totalOutput += Math.floor(group.getRunTime() / lineMaxCt * theoreticalOutput.getFlatNumber());
			}
		}

		// 创建并添加该小时的理论产出记录
		HourlyOutput output = new HourlyOutput();
		output.setTime(hour);
		output.setCount((int) Math.floor(totalOutput));
		outputs.add(output);
	}

	/**
	 * 获取直通下的不良类型统计
	 * @param query 查询条件
	 * @return 首序不良类型统计列表
	 */
	private List<DeviceDefectType> getThroughDefectTypes(AnalyzeQuery query, List<Device> aoiDevices) {
		List<DeviceDefectType> deviceDefectTypes = new ArrayList<>();
		// 遍历设备
		for (Device device : aoiDevices) {
			// 获取不良类型
			// List<DefectTypeInfo> defectTypeInfos =
			// AoiDefectTypesCalculator.getAoiDefectTypes(device, query,
			// mongoTemplate);
			List<DefectTypeInfo> defectTypeInfos = AoiDefectTypesWithMes.getFirstPassDefectTypes(device, query,
					mongoTemplate);
			DeviceDefectType deviceDefectType = BuilderUtil.builder(DeviceDefectType::new)
				.with(DeviceDefectType::setDevice, device)
				.with(DeviceDefectType::setDefectTypeInfos, defectTypeInfos)
				.build();
			deviceDefectTypes.add(deviceDefectType);
		}
		return deviceDefectTypes;
	}

	/**
	 * 获取复判下的不良类型统计
	 * @param query
	 * @param aoiDevices
	 * @return
	 */
	private List<DeviceDefectType> getRetrialDefectTypes(AnalyzeQuery query, List<Device> aoiDevices) {
		List<DeviceDefectType> deviceDefectTypes = new ArrayList<>();
		// 遍历设备
		for (Device device : aoiDevices) {
			List<DefectTypeInfo> defectTypeInfos = AoiDefectTypesWithMes.getRejudgedDefectTypes(device, query,
					mongoTemplate);
			DeviceDefectType deviceDefectType = BuilderUtil.builder(DeviceDefectType::new)
				.with(DeviceDefectType::setDevice, device)
				.with(DeviceDefectType::setDefectTypeInfos, defectTypeInfos)
				.build();
			deviceDefectTypes.add(deviceDefectType);
		}
		return deviceDefectTypes;
		// List<DeviceDefectType> deviceDefectTypes = new ArrayList<>();
		// // 遍历设备
		// for (Device device : aoiDevices) {
		// // 获取所有NG流水号
		// List<String> ngSerials = AoiNgCalculator.findNgSerials(device, query,
		// mongoTemplate);
		// if (ngSerials.isEmpty()) {
		// return Collections.emptyList();
		// }
		// List<String> localSamples = mesSampleService.findAllByLbIdIn(ngSerials)
		// .stream()
		// .map(FromMesSample::getLbId)
		// .collect(Collectors.toList());

		// // 从NG列表中排除标准件
		// List<String> serialsToAnalyze = ngSerials.stream()
		// .filter(serial -> !localSamples.contains(serial))
		// .collect(Collectors.toList());

		// // 步骤 2: 通过本地缓存过滤掉已知的PASS件
		// if (!serialsToAnalyze.isEmpty()) {
		// List<LabelLatestStatus> latestPassStatus =
		// lbwpService.findLatestPassStatusByLbIds(serialsToAnalyze);

		// Set<String> localPassSerials = latestPassStatus.stream()
		// .map(LabelLatestStatus::getLbId)
		// .collect(Collectors.toSet());
		// serialsToAnalyze = serialsToAnalyze.stream()
		// .filter(serial -> !localPassSerials.contains(serial))
		// .collect(Collectors.toList());
		// }

		// // 步骤 3: 对最终确认的NG流水号进行缺陷类型分析
		// List<DefectTypeInfo> finalDefectTypes =
		// AoiDefectTypesWithSerialCalculator.getAoiDefectTypes(device, query,
		// mongoTemplate, serialsToAnalyze);
		// DeviceDefectType deviceDefectType = BuilderUtil.builder(DeviceDefectType::new)
		// .with(DeviceDefectType::setDevice, device)
		// .with(DeviceDefectType::setDefectTypeInfos, finalDefectTypes)
		// .build();
		// deviceDefectTypes.add(deviceDefectType);
		// }
		// return deviceDefectTypes;
	}

	/**
	 * 计算不良数量
	 * @param deviceDefectTypes
	 * @return
	 */
	private Integer calculateDefectCount(List<DeviceDefectType> deviceDefectTypes) {
		return deviceDefectTypes.stream()
			.flatMap(device -> device.getDefectTypeInfos().stream())
			.mapToInt(info -> Integer.parseInt(info.getCount()))
			.sum();
	}

	/**
	 * 获取TOP报警
	 * @param query 查询条件
	 * @return TOP报警列表
	 */
	@Override
	public List<AlarmInfo> getTopAlarms(AnalyzeQuery query) {
		// 线体编码
		String code = query.getCode();

		// 参数校验与基础数据准备
		query.validateTimeRange();
		ProductionLine productionLine = productionLineService.findProductionLineBycode(query.getCode());
		query.setLineType(productionLine.getType());

		Criteria criteria = Criteria.where("startTime")
			.gte(query.getStartTime())
			.and("endTime")
			.lte(query.getEndTime())
			.and("lineCode")
			.is(code);

		// 构建聚合查询
		Aggregation aggregation = Aggregation.newAggregation(
				// 1.时间过滤条件
				Aggregation.match(criteria),
				// 2.计算时长（毫秒）
				Aggregation.project("classification").andExpression("endTime - startTime").as("durationInMillis"),
				// 3. 按 classification 分组
				Aggregation.group("classification").sum("durationInMillis").as("totalDurationMillis"),
				// 4. 转换为分钟并保留两位小数
				Aggregation.project()
					.and("_id")
					.as("type")
					.andExpression("round(totalDurationMillis / (1000 * 60) * 100) / 100")
					.as("num"),
				Aggregation.sort(Sort.Direction.DESC, "num"), // 5. 倒序排序
				Aggregation.limit(5) // 6. 限制 Top N
		);

		// 执行聚合查询并映射结果
		AggregationResults<AlarmInfo> results = mongoTemplate.aggregate(aggregation, "t_abnormal", AlarmInfo.class);

		// 获取查询结果
		List<AlarmInfo> mappedResults = results.getMappedResults();

		// 根据字典获取异常类型
		Dict dict = dictRepository.findByCode("ABNORMAL_CODE")
			.orElseThrow(() -> SysErrEnum.NOT_FOUND.exception("异常类型不存在"));

		// 构建异常类型映射
		Map<String, String> typeNameMap = dict.getItemList()
			.stream()
			.collect(Collectors.toMap(DictItem::getValue, DictItem::getLabel, (v1, v2) -> v1));

		// 设置报警类型名称
		mappedResults.forEach(alarm -> alarm.setName(typeNameMap.getOrDefault(alarm.getType(), "未知")));

		// 获取生产信息
		// Map<String, List<ProductionData>> productData = getProductData(query);

		// Double npmsmtAlarm = AlarmCalculator.getNpmsmtAlarm(productData);

		return mappedResults;
	}

	/**
	 * 获取实际ct最大的贴片机
	 * @param query 查询条件
	 * @return 实际ct最大的贴片机
	 */
	private Device getSmtByMaxCt(AnalyzeQuery query) {
		// 获取贴片机设备
		List<Device> devices = deviceService.getDevicesByCategory(query.getCode(), DeviceCategory.SMT);
		Device maxDevice = null;
		double maxCt = 0.0;
		for (Device device : devices) {
			double ct;
			if (Objects.requireNonNull(device.getType()) == DeviceType.smt_samsung) {
				ct = DeviceCtCalculator.getSamsungSmtCt(device, query, mongoTemplate);
			}
			else if (Objects.requireNonNull(device.getType() == DeviceType.smt_yamaha)) {
				ct = DeviceCtCalculator.getYamahaSmtCt(device, query, mongoTemplate);
			}
			else {
				ct = DeviceCtCalculator.getNpmSmtCt(device, query, mongoTemplate);
			}
			if (ct > maxCt) {
				maxCt = ct;
				maxDevice = device;
			}
		}
		return maxDevice;
	}

	private DefectTypeResult getDefectTypeResult(List<DeviceDefectType> defectTypes) {
		// 用于存储所有设备的缺陷类型统计结果
		List<Map<String, String>> allDefectTypes = new ArrayList<>();
		List<String> dimensions = new ArrayList<>();
		dimensions.add("type");
		List<Map<String, String>> heads = new ArrayList<>();

		// 遍历每个设备，获取缺陷类型数据
		for (int i = 0; i < defectTypes.size(); i++) {
			// 获取AOI不良类型数量
			List<DefectTypeInfo> aoiDefectTypes = defectTypes.get(i).getDefectTypeInfos();

			// 将 DefectTypeInfo 转换为 Map<String, String>
			for (DefectTypeInfo defectType : aoiDefectTypes) {
				// 查找是否已经存在该缺陷类型的记录
				Map<String, String> defectMap = allDefectTypes.stream()
					.filter(map -> map.get("type").equals(defectType.getType()))
					.findFirst()
					.orElseGet(() -> {
						// 使用 LinkedHashMap 确保 type 字段在最前面
						Map<String, String> newMap = new LinkedHashMap<>();
						newMap.put("type", defectType.getType()); // type 字段优先插入
						allDefectTypes.add(newMap);
						return newMap;
					});

				// 将当前设备的缺陷数量添加到对应的缺陷类型记录中
				defectMap.put("series" + (i + 1), defectType.getCount());
			}
			dimensions.add("series" + (i + 1));
		}

		// 生成表头信息
		for (int i = 0; i < defectTypes.size(); i++) {
			Map<String, String> map = new LinkedHashMap<>(); // 使用 LinkedHashMap 确保顺序
			map.put("type", "bar");
			map.put("name",
					(defectTypes.size() > 1 ? defectTypes.get(i).getDevice().getTrackEnum().getName() : "") + "不良数量");
			heads.add(map);
		}

		return BuilderUtil.builder(DefectTypeResult::new)
			.with(DefectTypeResult::setData, allDefectTypes)
			.with(DefectTypeResult::setDimensions, dimensions)
			.with(DefectTypeResult::setHeadMaps, heads)
			.build();
	}

	@Override
	public List<OeeResult> getWorkshopMonthlyOee(String workShopId, AnalyzeQuery query) {
		// 获取车间下所有线体
		List<ProductionLine> produtionLineByWorkshopCode = productionLineService
			.findProdutionLineByWorkshopCode(workShopId);
		// 查询每条线体的OEE
		return produtionLineByWorkshopCode.stream().map(line -> {
			AnalyzeQuery lineQuery = new AnalyzeQuery();
			lineQuery.setCode(line.getCode());
			lineQuery.setStartTime(query.getStartTime());
			lineQuery.setEndTime(query.getEndTime());
			// 其他必要参数可补充
			return getLineOee(lineQuery).getOeeResult();
		}).toList();
	}

	@Override
	public List<OeeResult> getWorkshopDailyOee(String workShopId, AnalyzeQuery query) {
		// 获取车间下所有线体
		List<ProductionLine> produtionLineByWorkshopCode = productionLineService
			.findProdutionLineByWorkshopCode(workShopId);
		// 查询每条线体的OEE
		return produtionLineByWorkshopCode.stream().map(line -> {
			AnalyzeQuery lineQuery = new AnalyzeQuery();
			lineQuery.setCode(line.getCode());
			if (query.getStartTime() != null && query.getEndTime() != null) {
				lineQuery.setStartTime(query.getStartTime());
				lineQuery.setEndTime(query.getEndTime());
			}
			else {
				lineQuery.generateTimeRange();
			}
			// 其他必要参数可补充
			return getLineOee(lineQuery).getOeeResult();
		}).toList();
	}

}
