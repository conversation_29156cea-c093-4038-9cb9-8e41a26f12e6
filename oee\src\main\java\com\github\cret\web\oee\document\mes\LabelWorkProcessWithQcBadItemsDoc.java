package com.github.cret.web.oee.document.mes;

import java.util.Date;
import java.util.List;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import com.github.cret.web.oee.domain.mes.QcBadItem;

/**
 * 标签工艺过程分析与QC不良项目组合数据文档
 */
@Document("t_mes_label_work_process_with_qc_bad_items")
public class LabelWorkProcessWithQcBadItemsDoc {

	@Id
	private String id;

	/**
	 * 标签ID
	 */
	@Field(name = "lbId")
	private String lbId;

	/**
	 * 标签组
	 */
	@Field(name = "lbGrp")
	private String lbGrp;

	/**
	 * 线体ID
	 */
	@Field(name = "plId")
	private String plId;

	/**
	 * 制造订单号
	 */
	@Field(name = "mo")
	private String mo;

	/**
	 * 关键工序首次结果（仅考虑SMT-03和SMT-06工序的最小IS_PASS值）
	 */
	@Field(name = "keyWpFirstResult")
	private String keyWpFirstResult;

	/**
	 * 最终结果（按完成时间排序的最后一个IS_PASS值）
	 */
	@Field(name = "finalResult")
	private String finalResult;

	/**
	 * 最终工序完成时间
	 */
	@Field(name = "finalWpCmpDate")
	private Date finalWpCmpDate;

	/**
	 * QC不良项目列表
	 */
	@Field(name = "qcBadItems")
	private List<QcBadItem> qcBadItems;

	/**
	 * 数据同步时间
	 */
	@Field(name = "syncTime")
	private Date syncTime;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getLbId() {
		return lbId;
	}

	public void setLbId(String lbId) {
		this.lbId = lbId;
	}

	public String getLbGrp() {
		return lbGrp;
	}

	public void setLbGrp(String lbGrp) {
		this.lbGrp = lbGrp;
	}

	public String getPlId() {
		return plId;
	}

	public void setPlId(String plId) {
		this.plId = plId;
	}

	public String getMo() {
		return mo;
	}

	public void setMo(String mo) {
		this.mo = mo;
	}

	public String getKeyWpFirstResult() {
		return keyWpFirstResult;
	}

	public void setKeyWpFirstResult(String keyWpFirstResult) {
		this.keyWpFirstResult = keyWpFirstResult;
	}

	public String getFinalResult() {
		return finalResult;
	}

	public void setFinalResult(String finalResult) {
		this.finalResult = finalResult;
	}

	public Date getFinalWpCmpDate() {
		return finalWpCmpDate;
	}

	public void setFinalWpCmpDate(Date finalWpCmpDate) {
		this.finalWpCmpDate = finalWpCmpDate;
	}

	public List<QcBadItem> getQcBadItems() {
		return qcBadItems;
	}

	public void setQcBadItems(List<QcBadItem> qcBadItems) {
		this.qcBadItems = qcBadItems;
	}

	public Date getSyncTime() {
		return syncTime;
	}

	public void setSyncTime(Date syncTime) {
		this.syncTime = syncTime;
	}

	@Override
	public String toString() {
		return "LabelWorkProcessWithQcBadItemsDoc{" +
				"id='" + id + '\'' +
				", lbId='" + lbId + '\'' +
				", lbGrp='" + lbGrp + '\'' +
				", plId='" + plId + '\'' +
				", mo='" + mo + '\'' +
				", keyWpFirstResult='" + keyWpFirstResult + '\'' +
				", finalResult='" + finalResult + '\'' +
				", finalWpCmpDate=" + finalWpCmpDate +
				", qcBadItems=" + qcBadItems +
				", syncTime=" + syncTime +
				'}';
	}
}
